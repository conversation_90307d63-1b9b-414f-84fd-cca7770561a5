import apiClient from '@/src/utils/apiClient';
import { PATIENT_ENDPOINTS } from '@/src/utils/apiRoutes';

export interface PatientDetails {
    id: number;
    doctor_id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    gender: string;
    ship_to_office: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    bill_to_office: {
        id: number;
        doctor_id: number;
        clinic_name: string;
        street_address: string;
        city: string;
        postal_code: string;
        phone_number: string;
        created_at: string;
        updated_at: string;
    };
    plan_id: number;
    clinical_conditions: string;
    general_notes: string;
    is_active: boolean;
    created_at: string;
    updated_at: string;
    stlFile1: string;
    stlFile2: string;
    cbctFile: string;
    profileRepose: string;
    buccalRight: string;
    buccalLeft: string;
    frontalRepose: string;
    frontalSmiling: string;
    labialAnterior: string;
    occlussalLower: string;
    occlussalUpper: string;
    radioGraph1: string;
    radioGraph2: string;
    data: {
        chief_complaint: string;
        treatment_goals: string;
        notes: string;
    };
    country: string;
    uuid: string | null;
    plan: {
        id: number;
        name: string;
        created_at: string;
        updated_at: string;
        type: string;
        duration_years: number | null;
        expiration_date: string | null;
    };
}

export interface Patient {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    dob: string;
    country: string;
    created_at: string;
    updated_at: string;
}

// List all patients
export const getAllPatients = async (): Promise<Patient[]> => {
    try {
        const response = await apiClient.get(PATIENT_ENDPOINTS.GET_PATIENTS);
        const { data } = response.data;
        if (!Array.isArray(data)) {
            throw new Error('Unexpected response structure');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not load patients';
        throw new Error(message);
    }
};

// View one patient by ID
export const viewPatient = async (patientId: number): Promise<PatientDetails> => {
    try {
        const response = await apiClient.get(PATIENT_ENDPOINTS.GET_PATIENT(patientId.toString()));
        const { data } = response.data;
        if (!data) {
            throw new Error('No patient data received');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not fetch patient';
        throw new Error(message);
    }
};
