"use client";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Link from "next/link";
import { IRootState } from "@/src/store";
import {
  toggleTheme,
  toggleSidebar,
  toggleRTL,
} from "@/src/store/themeConfigSlice";
import { useAuth } from "@/src/contexts/AuthContext";
import Dropdown from "@/src/components/dropdown";
import IconMenu from "@/src/components/icon/icon-menu";
import IconBellBing from "@/src/components/icon/icon-bell-bing";
import IconUser from "@/src/components/icon/icon-user";
import IconLogout from "@/src/components/icon/icon-logout";
import IconMenuDashboard from "@/src/components/icon/menu/icon-menu-dashboard";
import IconCaretDown from "@/src/components/icon/icon-caret-down";
import { useRouter } from "next/navigation";
import { getTranslation } from "@/i18n";

const Header = () => {
  const dispatch = useDispatch();
  const router = useRouter();
  const { t, i18n } = getTranslation();
  const { user, logout, isLoading } = useAuth();

  const isRtl =
    useSelector((state: IRootState) => state.themeConfig.rtlClass) === "rtl";

  const themeConfig = useSelector((state: IRootState) => state.themeConfig);

  // Handle logout
  const handleLogout = async () => {
    try {
      await logout();
    }
    catch (error) {
      console.error('Logout error:', error);
    }
  };

  const setLocale = (flag: string) => {
    if (flag.toLowerCase() === "ae") {
      dispatch(toggleRTL("rtl"));
    } else {
      dispatch(toggleRTL("ltr"));
    }
    router.refresh();
  };

  const [search, setSearch] = useState(false);

  return (
    <header
      className={`z-40 ${
        themeConfig.semidark && themeConfig.menu === "horizontal" ? "dark" : ""
      }`}
    >
      <div className="shadow-sm">
        <div className="relative flex w-full items-center bg-white px-5 py-4 dark:bg-black">
          <div className="horizontal-logo flex items-center justify-between ltr:mr-2 rtl:ml-2 lg:hidden">
            <Link href="/" className="main-logo flex shrink-0 items-center">
              <img
                className="inline w-[180px] ltr:-ml-1 rtl:-mr-1"
                src="/assets/images/logo.png"
                alt="logo"
              />
            </Link>
            <button
              type="button"
              className="collapse-icon flex flex-none rounded-full bg-white-light/40 p-2 hover:bg-white-light/90 hover:text-primary ltr:ml-2 rtl:mr-2 dark:bg-dark/40 dark:text-[#d0d2d6] dark:hover:bg-dark/60 dark:hover:text-primary lg:hidden"
              onClick={() => dispatch(toggleSidebar())}
            >
              <IconMenu className="h-5 w-5" />
            </button>
          </div>

          <div className="flex items-center space-x-1.5 ltr:ml-auto rtl:mr-auto rtl:space-x-reverse dark:text-[#d0d2d6] sm:flex-1 ltr:sm:ml-0 sm:rtl:mr-0 lg:space-x-2">
            <div className="sm:ltr:mr-auto sm:rtl:ml-auto">
              {/* Search form removed for simplicity */}
            </div>

            <div className="dropdown shrink-0">
              <Dropdown
                offset={[0, 8]}
                placement={`${isRtl ? "bottom-start" : "bottom-end"}`}
                btnClassName="relative block p-2 rounded-full bg-white-light/40 dark:bg-dark/40 hover:text-primary hover:bg-white-light/90 dark:hover:bg-dark/60"
                button={
                  <span>
                    <IconBellBing />
                    <span className="absolute top-0 flex h-3 w-3 ltr:right-0 rtl:left-0">
                      <span className="absolute -top-[3px] inline-flex h-full w-full animate-ping rounded-full bg-success/50 opacity-75 ltr:-left-[3px] rtl:-right-[3px]"></span>
                      <span className="relative inline-flex h-[6px] w-[6px] rounded-full bg-success"></span>
                    </span>
                  </span>
                }
              >
                {/* Notifications content removed for simplicity */}
                <ul className="w-[300px] divide-y !py-0 text-dark dark:divide-white/10 dark:text-white-dark sm:w-[350px]">
                  <li onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center justify-between px-4 py-2 font-semibold">
                      <h4 className="text-lg">Notification</h4>
                      {/* Simplified notification count */}
                      <span className="badge bg-primary/80">0 New</span>
                    </div>
                  </li>
                  <li onClick={(e) => e.stopPropagation()}>
                    <button
                      type="button"
                      className="!grid min-h-[200px] place-content-center text-lg hover:!bg-transparent"
                    >
                      <div className="mx-auto mb-4 rounded-full ring-4 ring-primary/30">
                        {/* IconInfoCircle removed */}
                      </div>
                      No data available.
                    </button>
                  </li>
                </ul>
              </Dropdown>
            </div>
            <div className="dropdown flex shrink-0">
              <Dropdown
                offset={[0, 8]}
                placement={`${isRtl ? "bottom-start" : "bottom-end"}`}
                btnClassName="relative group block"
                button={
                  <img
                    className="h-9 w-9 rounded-full object-cover saturate-50 group-hover:saturate-100"
                    src="/assets/images/user-profile.jpeg"
                    alt="userProfile"
                  />
                }
              >
                <ul className="w-[230px] !py-0 font-semibold text-dark dark:text-white-dark dark:text-white-light/90">
                  <li>
                    <div className="flex items-center px-4 py-4">
                      <img
                        className="h-10 w-10 rounded-md object-cover"
                        src="/assets/images/user-profile.jpeg"
                        alt="userProfile"
                      />
                      <div className="truncate ltr:pl-4 rtl:pr-4">
                        <h4 className="text-base">
                          {user?.name || 'Admin User'}
                          <span className="rounded bg-success-light px-1 text-xs text-success ltr:ml-2 rtl:ml-2">
                            Admin
                          </span>
                        </h4>
                        <button
                          type="button"
                          className="text-black/60 hover:text-primary dark:text-dark-light/60 dark:hover:text-white"
                        >
                          {user?.email || '<EMAIL>'}
                        </button>
                      </div>
                    </div>
                  </li>
                  <li>
                    <Link
                      href="/profile"
                      className="dark:hover:text-white"
                    >
                      {/* IconUser removed */}
                      Profile
                    </Link>
                  </li>
                  <li className="border-t border-white-light dark:border-white-light/10">
                    <button
                      type="button"
                      onClick={handleLogout}
                      disabled={isLoading}
                      className="!py-3 text-danger w-full text-left flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <IconLogout className="h-4.5 w-4.5 shrink-0 rotate-90 ltr:mr-2 rtl:ml-2" />
                      {isLoading ? 'Signing Out...' : 'Sign Out'}
                    </button>
                  </li>
                </ul>
              </Dropdown>
            </div>
          </div>
        </div>

        {/* horizontal menu */}
        <ul className="horizontal-menu hidden border-t border-[#ebedf2] bg-white px-6 py-1.5 font-semibold text-black rtl:space-x-reverse dark:border-[#191e3a] dark:bg-black dark:text-white-dark lg:space-x-1.5 xl:space-x-8">
          <li className="menu nav-item relative">
            <button type="button" className="nav-link">
              <div className="flex items-center">
                <IconMenuDashboard className="shrink-0" />
                <span className="px-1">{t("dashboard")}</span>
              </div>
              <div className="right_arrow">
                <IconCaretDown />
              </div>
            </button>
            <ul className="sub-menu">
              <li>
                <Link href="/">{t("sales")}</Link>
              </li>
              <li>
                <Link href="/analytics">{t("analytics")}</Link>
              </li>
              <li>
                <Link href="/finance">{t("finance")}</Link>
              </li>
              <li>
                <Link href="/crypto">{t("crypto")}</Link>
              </li>
            </ul>
          </li>
          {/* Removed Apps, Components, Tables, Forms, Pages, and More menus */}
        </ul>
      </div>
    </header>
  );
};

export default Header;
