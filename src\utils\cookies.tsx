// utils/cookie.ts
'use client';

import Cookies from 'js-cookie';
import CryptoJs from 'crypto-js';

const _key = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "7d7cd92a9c3055f30f8943b5092abb8e";

const cookiesToClear = [
  'accessAdminToken',
  'user',
  'authToken',
  'email',
  'role',
  'username',
  'userId',
  'firstname',
  'lastname',
  'country',
  'profileImage'
];

/**
 * Encrypts and stores a token in a cookie, expiring when the JWT does.
 */
export function storeCookies(name: string, token: any) {
  const encrypted = CryptoJs.AES.encrypt(JSON.stringify(token), _key).toString();
  const now = Math.floor(Date.now() / 1000);
  const days = Math.ceil((token.exp - now) / (24 * 60 * 60));

  Cookies.set(name, encrypted, {
    expires: days,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  });
}

/**
 * Reads, decrypts, and parses a cookie-stored token.
 */
export function getCookies(name: string): any | null {
  const raw = Cookies.get(name);
  if (!raw) return null;

  try {
    const bytes = CryptoJs.AES.decrypt(raw, _key);
    const json = bytes.toString(CryptoJs.enc.Utf8);
    const token = JSON.parse(json);

    // expire & cleanup
    if (token.exp && Math.floor(Date.now() / 1000) >= token.exp) {
      clearAllCookies();
      return null;
    }

    return token;
  } catch {
    return null;
  }
}

/**
 * Deletes all known auth-related cookies.
 */
export function clearAllCookies() {
  cookiesToClear.forEach((c) => {
    if (Cookies.get(c)) {
      Cookies.remove(c, { path: '/' });
    }
  });
}

export default getCookies;