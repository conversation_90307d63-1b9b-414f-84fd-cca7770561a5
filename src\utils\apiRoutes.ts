/**
 * API Endpoints for the application
 */

import { log } from "console";

// Base API URL
const baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:5008";
console.log(baseUrl);
export const API_BASE_URL = `${baseUrl}/api/v1`;
console.log(API_BASE_URL);
// Auth endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: "/auth/login-admin",
  LOGOUT: "/auth/logout",
  FORGOT_PASSWORD: "/auth/forgot-password",
  RESET_PASSWORD: "/auth/reset-password",
  GET_PROFILE: "/auth/profile",
  UPDATE_PROFILE: "/auth/profile",
};

// User endpoints
export const USER_ENDPOINTS = {
  GET_USERS: "/admin/users",
  GET_USER: (id: string) => `/users/${id}`,
  CREATE_USER: "/users",
  UPDATE_USER: (id: string) => `/users/${id}`,
  DELETE_USER: (id: string) => `/users/${id}`,
};

// Dashboard endpoints
export const DASHBOARD_ENDPOINTS = {
  GET_STATS: "/admin/dashboard/statistics",
};

// Settings endpoints
export const SETTINGS_ENDPOINTS = {
  GET_SETTINGS: "/admin/settings",
  UPDATE_SETTINGS: "/admin/settings",
};

export const DRIVERS_ENDPOINTS = {
  ADD_DRIVER: "/admin/drivers",
  UPDATE_DRIVER: "/admin/drivers",
  VIEW_DRIVER: "/admin/drivers",
  ALL_DRIVERS: "/admin/drivers",
};

export const SHIPMENTS_ENDPOINTS = {
  GET_SHIPMENTS: "/admin/shipments", // GET    all shipments
  ADD_SHIPMENT: "/admin/shipments", // POST   new shipment
  VIEW_SHIPMENT: "/admin/shipments", // GET    /:id
  UPDATE_SHIPMENT: "/admin/shipments", // PATCH  /:id
  DELETE_SHIPMENT: "/admin/shipments", // DELETE /:id
  CHANGE_DRIVER: "/admin/shipments/change/tagged-driver", // PATCH /:container_id
  REMOVE_DRIVER: "/admin/shipments/remove/tagged-driver", // PATCH /:container_id
  UPDATE_CONTAINER_STATUS: "/admin/shipments/toggle-status", // PATCH /:container_id
  VERIFY_DELIVERY_OTP: "/admin/shipments/container/verify-otp", // POST /:container_id
  CONFIRM_DELIVERY: "/admin/shipments/container/confirm-delivery", // POST /:container_id
};

// Task details endpoints
export const TASK_ENDPOINTS = {
  GET_TASKS: "/admin/tasks", // GET    all tasks
  UPDATE_TASK: "/admin/tasks", // PATCH  /:id
  GET_DELIVERY_PROOF: "/admin/tasks/delivery-proof", // GET /:container_id
  SUBMIT_DELIVERY_PROOF: "/admin/tasks/delivery-proof", // POST /:container_id
};

// Notification endpoints
export const NOTIFICATION_ENDPOINTS = {
  GET_NOTIFICATIONS: "/admin/notifications", // GET all notifications
  MARK_AS_READ: "/admin/notifications", // PATCH /:id/read
  MARK_ALL_AS_READ: "/admin/notifications/read-all", // PATCH
};

// Doctor endpoints
export const DOCTOR_ENDPOINTS = {
    GET_DOCTORS: '/user/doctors',
    CREATE_DOCTOR: '/auth/register-user',
    UPDATE_DOCTOR: (doctorId: string) => `/user/${doctorId}`,
    GET_DOCTOR: (doctorId: string) => `/user/${doctorId}`,
    DELETE_DOCTOR: (doctorId: string) => `/user/${doctorId}`,
};

export const PLANS_ENDPOINTS = {
    GET_PLANS: '/admin/plan',
    CREATE_PLAN: '/admin/plan',
    UPDATE_PLAN: (planId: string) => `/admin/plan/${planId}`,
    GET_PLAN: (planId: string) => `/admin/plan/${planId}`,
    DELETE_PLAN: (planId: string) => `/admin/plan/${planId}`,
};

// Patient endpoints
export const PATIENT_ENDPOINTS = {
    GET_PATIENTS: '/user/patients',
    GET_PATIENT: (patientId: string) => `/user/patients/${patientId}`,
};

// Other endpoints can be added here
