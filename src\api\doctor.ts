import apiClient from '@/src/utils/apiClient';
import { DOCTOR_ENDPOINTS } from '@/src/utils/apiRoutes';

export interface Doctor {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
    created_at: string;
    updated_at: string;
}

export interface CreateDoctorInput {
    first_name: string;
    last_name: string;
    email: string;
    username: string;
    doctor_addresses?: string;
}

// List doctors
export const getAllDoctors = async (): Promise<Doctor[]> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTORS);
        const { data } = response.data;
        if (!Array.isArray(data)) {
            throw new Error('Unexpected response structure');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not load doctors';
        throw new Error(message);
    }
};

// Create doctor
export const createDoctor = async (doctorData: CreateDoctorInput): Promise<Doctor> => {
    try {
        const response = await apiClient.post(DOCTOR_ENDPOINTS.CREATE_DOCTOR, doctorData);
        const { data } = response.data;
        if (!data) {
            throw new Error('No data returned after creation');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not create doctor';
        if (apiError?.data?.errors) {
            const fieldErrors = Object.entries(apiError.data.errors)
                .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                .join(' | ');
            message += ` (${fieldErrors})`;
        }
        throw new Error(message);
    }
};

// Update doctor
export const updateDoctor = async (doctorId: number, doctorData: Partial<CreateDoctorInput>): Promise<Doctor> => {
    try {
        const response = await apiClient.patch(DOCTOR_ENDPOINTS.UPDATE_DOCTOR(doctorId.toString()), doctorData);
        const { data } = response.data;
        if (!data) {
            throw new Error('No data returned after update');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not update doctor';
        if (apiError?.data?.errors) {
            const fieldErrors = Object.entries(apiError.data.errors)
                .map(([field, err]) => `${field}: ${(err as string[]).join(', ')}`)
                .join(' | ');
            message += ` (${fieldErrors})`;
        }
        throw new Error(message);
    }
};

// View one doctor
export const viewDoctor = async (doctorId: number): Promise<Doctor> => {
    try {
        const response = await apiClient.get(DOCTOR_ENDPOINTS.GET_DOCTOR(doctorId.toString()));
        const { data } = response.data;
        if (!data) {
            throw new Error('No doctor data received');
        }
        return data;
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not fetch doctor';
        throw new Error(message);
    }
};

// Delete doctor
export const deleteDoctor = async (doctorId: number): Promise<void> => {
    try {
        await apiClient.delete(DOCTOR_ENDPOINTS.DELETE_DOCTOR(doctorId.toString()));
    } catch (error: any) {
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not delete doctor';
        throw new Error(message);
    }
};
