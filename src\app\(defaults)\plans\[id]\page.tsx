'use client';

import React from 'react';
import { useSearchParams, useParams } from 'next/navigation';
import PlansForm from '@/src/components/pages/plans/PlansForm';

const PlanPage: React.FC = () => {
    const { id } = useParams();
    const searchParams = useSearchParams();
    const planId = Number(id);
    const mode = searchParams.get('mode') as 'view' | 'edit' | undefined;

    return (
        <div className="p-4 bg-white rounded shadow">
            <h2 className="text-2xl font-bold mb-4">
                {mode === 'view' ? 'View Plan' : mode === 'edit' ? 'Edit Plan' : 'Plan'}
            </h2>
            <PlansForm planId={planId} mode={mode} />
        </div>
    );
};

export default PlanPage;
