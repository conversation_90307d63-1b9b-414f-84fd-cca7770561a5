'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { authService, User } from '@/src/api/auth';
import toast from 'react-hot-toast';
import Loading from '@/src/components/layouts/loading';

const ProfilePage = () => {
    const router = useRouter();
    const [profile, setProfile] = useState<Partial<User>>({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchProfile = async () => {
            try {
                const data = await authService.getProfile();
                setProfile(data);
            } catch (err: any) {
                setError(err.message);
                toast.error(err.message);
            } finally {
                setLoading(false);
            }
        };
        fetchProfile();
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        setProfile((prev) => ({ ...prev, [name]: value }));
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            const file = e.target.files[0];
            // Handle file upload logic here, e.g., upload to a server and get the URL
            // For now, we'll just display it locally
            const reader = new FileReader();
            reader.onloadend = () => {
                setProfile((prev) => ({ ...prev, profile_image: reader.result as string }));
            };
            reader.readAsDataURL(file);
        }
    };


    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        try {
            await authService.updateProfile(profile);
            toast.success('Profile updated successfully!');
        } catch (err: any) {
            setError(err.message);
            toast.error(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (loading) return <Loading />;
    if (error) return <div className="text-red-600 p-4">{error}</div>;

    return (
        <>
            <h2 className="text-2xl font-bold mb-4">Edit Profile</h2>
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label htmlFor="name" className="font-medium">Full Name:</label>
                        <input
                            id="name"
                            name="name"
                            value={profile.name || ''}
                            onChange={handleInputChange}
                            className="border p-2 w-full rounded-md mt-1"
                        />
                    </div>
                    <div>
                        <label htmlFor="email" className="font-medium">Email:</label>
                        <input
                            id="email"
                            name="email"
                            type="email"
                            value={profile.email || ''}
                            onChange={handleInputChange}
                            className="border p-2 w-full rounded-md mt-1"
                        />
                    </div>
                    <div className="col-span-1 md:col-span-2">
                        <label htmlFor="profile_image" className="font-medium">Profile Image:</label>
                        <div className="flex items-center gap-4 mt-1">
                            {profile.profile_image && (
                                <img src={profile.profile_image} alt="Profile" className="h-24 w-24 rounded-full object-cover" />
                            )}
                            <input
                                id="profile_image"
                                name="profile_image"
                                type="file"
                                onChange={handleFileChange}
                                className="border p-2 w-full rounded-md"
                            />
                        </div>
                    </div>
                </div>
                <div className="flex gap-4 mt-6">
                    <button
                        type="submit"
                        className="bg-blue-600 text-white px-6 py-2 rounded-full hover:bg-blue-700 transition-colors duration-200"
                        disabled={loading}
                    >
                        {loading ? 'Saving...' : 'Save Changes'}
                    </button>
                    <button
                        type="button"
                        onClick={() => router.back()}
                        className="border border-gray-400 text-gray-700 px-6 py-2 rounded-full hover:bg-gray-100 transition-colors duration-200"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </>
    );
};

export default ProfilePage;
