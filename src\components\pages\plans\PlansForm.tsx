'use client';

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { useRouter, useSearchParams } from 'next/navigation';
import { createPlan, updatePlan, viewPlan, Plan } from '@/src/api/plans';
import toast from 'react-hot-toast';

interface PlansFormProps {
    planId?: number;
    mode?: 'view' | 'edit';
}

const PlansForm: React.FC<PlansFormProps> = ({ planId, mode: propMode }) => {
    const {
        control,
        handleSubmit,
        setValue,
        formState: { errors },
    } = useForm<Plan>();
    const router = useRouter();
    const searchParams = useSearchParams();
    const mode = propMode || searchParams.get('mode');
    const isViewMode = mode === 'view';

    const [loading, setLoading] = useState(false);
    const [plan, setPlan] = useState<Plan | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [planType, setPlanType] = useState<string>('');

    useEffect(() => {
        if (planId) {
            const fetchPlan = async () => {
                try {
                    setLoading(true);
                    const data = await viewPlan(planId);
                    setPlan(data);
                    setValue('name', data.name);
                    setValue('type', data.type);
                    setValue('duration_years', data.duration_years);
                    setPlanType(data.type);
                } catch (error) {
                    setError('Failed to fetch plan details');
                } finally {
                    setLoading(false);
                }
            };
            fetchPlan();
        }
    }, [planId, setValue]);

    const onSubmit = async (data: Plan) => {
        try {
            setLoading(true);
            setError(null);

            // Remove duration_years if type is 'retainer'
            const payload = { ...data };
            if (payload.type === 'retainer') {
                delete payload.duration_years;
            }

            if (planId) {
                await updatePlan(planId, payload);
                toast.success('Plan updated successfully');
            } else {
                await createPlan(payload);
                toast.success('Plan created successfully');
            }
            router.push('/plans');
        } catch (error: any) {
            // Show backend error message if available
            setError(error.message || 'An error occurred');
            toast.error(error.message || 'An error occurred'); // <-- Add this line
        } finally {
            setLoading(false);
        }
    };

    const handleTypeChange = (value: string) => {
        setValue('type', value);
        setPlanType(value);
    };

    if (isViewMode && plan) {
        return (
            <div className="mx-auto bg-white p-6 ">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Plan Details</h2>
                    <div className="flex gap-2">
                        <button onClick={() => router.push(`/plans/${planId}?mode=edit`)} className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]">
                            Edit Plan
                        </button>
                        <button onClick={() => router.back()} className="border border-[#7B7B7B] text-[#444443] px-5 py-1 rounded-full hover:bg-[#ebebeb] transition-colors duration-200">
                            Back
                        </button>
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-6">
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Name</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{plan.name}</div>
                    </div>
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Type</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{plan.type}</div>
                    </div>
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Duration (years)</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{plan.duration_years}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mx-auto bg-white p-6 ">
            <h2 className="text-2xl font-bold mb-6">{planId ? 'Edit Plan' : 'Add Plan'}</h2>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    {/* Name */}
                    <div>
                        <label htmlFor="name" className="block mb-1 font-semibold">
                            Name
                        </label>
                        <Controller
                            name="name"
                            control={control}
                            rules={{ required: 'Name is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    id="name"
                                    placeholder="Enter plan name"
                                    className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                                    disabled={isViewMode}
                                />
                            )}
                        />
                        {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>}
                    </div>
                    {/* Type */}
                    <div>
                        <label htmlFor="type" className="block mb-1 font-semibold">
                            Type
                        </label>
                        <Controller
                            name="type"
                            control={control}
                            rules={{ required: 'Type is required' }}
                            render={({ field }) => (
                                <select
                                    {...field}
                                    id="type"
                                    className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                                    // Disable in view OR edit mode
                                    disabled={isViewMode || mode === 'edit'}
                                    onChange={(e) => handleTypeChange(e.target.value)}
                                >
                                    <option value="">Select type</option>
                                    <option value="aligner">Aligner</option>
                                    <option value="retainer">Retainer</option>
                                </select>
                            )}
                        />
                        {errors.type && <p className="text-sm text-red-600 mt-1">{errors.type.message}</p>}
                    </div>
                    {/* Duration (years) */}
                    {planType !== 'retainer' && (
                        <div>
                            <label htmlFor="duration_years" className="block mb-1 font-semibold">
                                Duration (years)
                            </label>
                            <Controller
                                name="duration_years"
                                control={control}
                                rules={{
                                    required: 'Duration is required',
                                    pattern: { value: /^[0-9]*$/, message: 'Please enter a valid number' },
                                    max: { value: 10, message: 'Duration cannot exceed 10 years' }
                                }}
                                render={({ field }) => (
                                    <input
                                        {...field}
                                        id="duration_years"
                                        type="number"
                                        placeholder="Enter duration in years"
                                        className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                                        disabled={isViewMode}
                                    />
                                )}
                            />
                            {errors.duration_years && <p className="text-sm text-red-600 mt-1">{errors.duration_years.message}</p>}
                        </div>
                    )}
                </div>

                <div className="flex gap-4 pt-4">
                    <button type="submit" disabled={loading} className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]">
                        {loading ? 'Saving...' : planId ? 'Update Plan' : 'Create Plan'}
                    </button>
                    <button
                        type="button"
                        onClick={() => router.back()}
                        disabled={loading}
                        className="border border-[#7B7B7B] text-[#444443] px-5 py-1 rounded-full hover:bg-[#ebebeb] transition-colors duration-200"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default PlansForm;
