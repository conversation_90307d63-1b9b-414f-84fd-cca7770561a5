import apiClient from '@/src/utils/apiClient';
import { PLANS_ENDPOINTS } from '@/src/utils/apiRoutes';

export interface Plan {
    id: number;
    name: string;
    type: string;
    duration_years: number | null;
    expiration_date: string | null;
    created_at: string;
    updated_at: string;
}

export interface CreatePlanInput {
    name: string;
    type: string;
    duration_years?: number | null;
    expiration_date?: string | null;
}

// List plans
export const getAllPlans = async (): Promise<Plan[]> => {
    try {
        const response = await apiClient.get(PLANS_ENDPOINTS.GET_PLANS);
        console.log('Plans Response:', response.data); // Debug

        const raw = response.data;
        const plansArray = raw?.data?.plans;

        if (raw.message?.includes("fetched") && Array.isArray(plansArray)) {
            return plansArray;
        } else {
            throw new Error('Unexpected response structure');
        }
    } catch (error: any) {
        console.error('getAllPlans Error:', error?.response?.data || error.message);
        throw new Error(error.response?.data?.message || 'Could not load plans');
    }
};



export const createPlan = async (planData: CreatePlanInput): Promise<Plan> => {
    try {
        const response = await apiClient.post(PLANS_ENDPOINTS.CREATE_PLAN, planData);
        const { data } = response.data;
        if (!data || typeof data !== 'object') {
            throw new Error('Unexpected response structure: data is not an object');
        }
        return data;
    } catch (error: any) {
        // Extract validation errors if present
        const apiError = error.response?.data;
        let message = apiError?.message || 'Could not create plan';

        // If there are field errors, append them to the message
        if (apiError?.data?.errors) {
            const fieldErrors = apiError.data.errors;
            // Convert errors object to readable string
            const errorMessages = Object.entries(fieldErrors)
                .map(([field, err]) => {
                    if (Array.isArray(err)) {
                        return `${field}: ${err.join(', ')}`;
                    } else if (typeof err === 'object' && err?._errors) {
                        return `${field}: ${err._errors.join(', ')}`;
                    }
                    return `${field}: ${JSON.stringify(err)}`;
                })
                .join(' | ');
            message += ` (${errorMessages})`;
        }

        throw new Error(message);
    }
};



// Update plan
export const updatePlan = async (planId: number, planData: Partial<CreatePlanInput>): Promise<Plan> => {
    try {
        const response = await apiClient.patch(PLANS_ENDPOINTS.UPDATE_PLAN(planId.toString()), planData);
        const { data } = response.data;
        if (!data) {
            throw new Error('No data returned after update');
        }
        return data;
    } catch (error: any) {
        throw new Error(error.response?.data?.message || 'Could not update plan');
    }
};

// View one plan
export const viewPlan = async (planId: number): Promise<Plan> => {
    try {
        const response = await apiClient.get(PLANS_ENDPOINTS.GET_PLAN(planId.toString()));
        const { data } = response.data;
        if (!data) {
            throw new Error('No plan data received');
        }
        return data;
    } catch (error: any) {
        throw new Error(error.response?.data?.message || 'Could not fetch plan');
    }
};

// Delete plan
export const deletePlan = async (planId: number): Promise<void> => {
    try {
        await apiClient.delete(PLANS_ENDPOINTS.DELETE_PLAN(planId.toString()));
    } catch (error: any) {
        throw new Error(error.response?.data?.message || 'Could not delete plan');
    }
};
