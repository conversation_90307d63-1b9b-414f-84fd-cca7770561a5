'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Doctor, CreateDoctorInput, viewDoctor, createDoctor, updateDoctor } from '@/src/api/doctor';
import toast from 'react-hot-toast';
import Loading from '../../layouts/loading';

interface DoctorModuleProps {
    mode?: 'add' | 'edit' | 'view';
    id?: string;
}

const DoctorModule: React.FC<DoctorModuleProps> = ({ mode = 'add', id }) => {
    const router = useRouter();
    const [loading, setLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [doctor, setDoctor] = useState<Doctor | null>(null);
    const [formErrors, setFormErrors] = useState<Partial<Record<keyof CreateDoctorInput, string>>>({});

    const [formData, setFormData] = useState<CreateDoctorInput>({
        first_name: '',
        last_name: '',
        email: '',
        username: '',
    });

    useEffect(() => {
        if ((mode === 'edit' || mode === 'view') && id) {
            setLoading(true);
            viewDoctor(Number(id))
                .then((d) => {
                    setDoctor(d);
                    if (mode === 'edit') {
                        setFormData({
                            first_name: d.first_name,
                            last_name: d.last_name,
                            email: d.email,
                            username: d.username,
                        });
                    }
                })
                .catch((err: any) => {
                    setError(err.message);
                    toast.error(err.message || 'Failed to load doctor details');
                    setTimeout(() => router.push('/doctor'), 2000);
                })
                .finally(() => setLoading(false));
        }
    }, [mode, id, router]);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, value } = e.target;
        let newValue = value;
        if (name === 'username') {
            newValue = value.replace(/\s/g, ''); // Remove all spaces from username
        }
        setFormData((prev) => ({
            ...prev,
            [name]: newValue,
        }));
        setFormErrors((prev) => ({
            ...prev,
            [name]: '',
        }));
    };

    const validateForm = () => {
        const errors: typeof formErrors = {};

        if (!formData.first_name.trim()) errors.first_name = 'First name is required';
        if (!formData.last_name.trim()) errors.last_name = 'Last name is required';
        if (!formData.email.trim()) errors.email = 'Email is required';
        if (!formData.username.trim()) errors.username = 'Username is required';

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setError(null);

        if (!validateForm()) return;

        setLoading(true);

        try {
            if (mode === 'add') {
                await createDoctor(formData);
            } else {
                await updateDoctor(Number(id), formData);
            }
            toast.success(`Doctor ${mode === 'add' ? 'created' : 'updated'} successfully!`);

            router.push('/doctor');
        } catch (err: any) {
            const errorMessage = err?.response?.data?.message || err.message || 'An error occurred';
            toast.error(errorMessage);
            setError(errorMessage);
        } finally {
            setLoading(false);
        }
    };

    const handleCancel = () => router.push('/doctor');
    const handleEdit = () => router.push(`/doctor/${id}/?mode=edit`);
    const handleBack = () => router.push('/doctor');

    if (loading) {
        return <Loading />;
    }

    if (error && mode === 'view') {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="text-red-600 text-center">
                    <h2 className="text-xl font-bold mb-2">Error</h2>
                    <p>{error}</p>
                    <p>Redirecting to doctor list...</p>
                </div>
            </div>
        );
    }

    if (mode === 'view' && doctor) {
        return (
            <div className="mx-auto bg-white p-6 rounded shadow-md">
                <div className="flex justify-between items-center mb-6">
                    <h2 className="text-2xl font-bold">Doctor Details</h2>
                    <div className="flex gap-2">
                        <button onClick={handleEdit} className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]">
                            Edit Doctor
                        </button>
                        <button onClick={handleBack} className="border border-[#7B7B7B] text-[#444443] px-5 py-1 rounded-full hover:bg-[#ebebeb] transition-colors duration-200">
                            Back
                        </button>
                    </div>
                </div>
                <div className="grid grid-cols-2 gap-6">
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">First Name</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.first_name}</div>
                    </div>
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Last Name</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.last_name}</div>
                    </div>
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Email</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.email}</div>
                    </div>
                    <div>
                        <label className="block mb-1 font-semibold text-gray-700">Username</label>
                        <div className="w-full border border-gray-300 px-4 py-2 rounded bg-gray-50">{doctor.username}</div>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="mx-auto bg-white p-6 rounded shadow-md">
            <h2 className="text-2xl font-bold mb-6">{mode === 'add' ? 'Add Doctor' : 'Edit Doctor'}</h2>
            <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    {/* First Name */}
                    <div>
                        <label className="block mb-1 font-semibold">First Name</label>
                        <input
                            type="text"
                            name="first_name"
                            value={formData.first_name}
                            onChange={handleInputChange}
                            placeholder="Enter first name"
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                        />
                        {formErrors.first_name && <p className="text-sm text-red-600 mt-1">{formErrors.first_name}</p>}
                    </div>
                    {/* Last Name */}
                    <div>
                        <label className="block mb-1 font-semibold">Last Name</label>
                        <input
                            type="text"
                            name="last_name"
                            value={formData.last_name}
                            onChange={handleInputChange}
                            placeholder="Enter last name"
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                        />
                        {formErrors.last_name && <p className="text-sm text-red-600 mt-1">{formErrors.last_name}</p>}
                    </div>
                    {/* Email */}
                    <div>
                        <label className="block mb-1 font-semibold">Email</label>
                        <input
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            placeholder="Enter email"
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                        />
                        {formErrors.email && <p className="text-sm text-red-600 mt-1">{formErrors.email}</p>}
                    </div>
                    {/* Username */}
                    <div>
                        <label className="block mb-1 font-semibold">Username</label>
                        <input
                            type="text"
                            name="username"
                            value={formData.username}
                            onChange={handleInputChange}
                            placeholder="Enter username"
                            className="w-full border border-gray-300 px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-orange-500"
                            disabled={loading}
                        />
                        {formErrors.username && <p className="text-sm text-red-600 mt-1">{formErrors.username}</p>}
                    </div>
                </div>
                <div className="flex gap-4 pt-4">
                    <button type="submit" disabled={loading} className="border border-red-600 text-white px-5 py-1 rounded-full hover:bg-red-100  hover:text-[#f36e22] transition-colors duration-200 bg-[#eb6309]">
                        {loading ? 'Saving...' : mode === 'add' ? 'Create Doctor' : 'Update Doctor'}
                    </button>
                    <button
                        type="button"
                        onClick={handleCancel}
                        disabled={loading}
                        className="border border-[#7B7B7B] text-[#444443] px-5 py-1 rounded-full hover:bg-[#ebebeb] transition-colors duration-200"
                    >
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    );
};

export default DoctorModule;
