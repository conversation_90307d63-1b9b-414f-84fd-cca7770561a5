// utils/tokenValidation.ts
import CryptoJs from 'crypto-js';

const _key = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "7d7cd92a9c3055f30f8943b5092abb8e";

/**
 * Decrypts and validates a token string from cookie.
 */
export function validateToken(token: string): boolean {
  try {
    // decrypt
    const bytes = CryptoJs.AES.decrypt(token, _key);
    const json = bytes.toString(CryptoJs.enc.Utf8);
    const payload = JSON.parse(json);

    // basic shape check
    if (!payload || typeof payload !== 'object' || !payload.exp) {
      return false;
    }

    // expiry
    const now = Math.floor(Date.now() / 1000);
    return payload.exp > now;
  } catch {
    return false;
  }
}